:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242425;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
  width: 100%;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vanilla:hover {
  filter: drop-shadow(0 0 2em #f7df1eaa);
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* TipTap Editor Styles */
.editor-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

/* Integration Fixes - Works for both local and Bubble */
#app,
[id^="tiptap-editor-"] {
  box-sizing: border-box;
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  overflow: visible;
  border-radius: 6px;
}

/* For Bubble - override Bubble's default styles with !important */
[id^="tiptap-editor-"] {
  box-sizing: border-box !important;
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif !important;
  overflow: visible !important;
  border-radius: 6px !important;
}

#app *,
[id^="tiptap-editor-"] * {
  box-sizing: border-box;
}

/* For Bubble - add !important */
[id^="tiptap-editor-"] * {
  box-sizing: border-box !important;
}

/* Ensure the main container doesn't clip the menu/toolbar */
.converted-content,
[id^="tiptap-editor-"] .converted-content {
  overflow: visible;
  border-radius: 6px;
}

/* For Bubble - add !important */
[id^="tiptap-editor-"] .converted-content {
  overflow: visible !important;
  border-radius: 6px !important;
}

/* Force consistent typography in Bubble */
[id^="tiptap-editor-"] .ProseMirror {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif !important;
  line-height: 1.6 !important;
}

/* Force heading styles to match local development */
[id^="tiptap-editor-"] .ProseMirror h1,
[id^="tiptap-editor-"] .ProseMirror h2,
[id^="tiptap-editor-"] .ProseMirror h3,
[id^="tiptap-editor-"] .ProseMirror h4,
[id^="tiptap-editor-"] .ProseMirror h5,
[id^="tiptap-editor-"] .ProseMirror h6 {
  font-weight: bold !important;
  margin: 1.2em 0 0.6em 0 !important;
  line-height: 1.3 !important;
}

[id^="tiptap-editor-"] .ProseMirror h1 {
  font-size: 2em !important;
}

[id^="tiptap-editor-"] .ProseMirror h2 {
  font-size: 1.5em !important;
}

[id^="tiptap-editor-"] .ProseMirror h3 {
  font-size: 1.17em !important;
}

[id^="tiptap-editor-"] .ProseMirror h4 {
  font-size: 1em !important;
}

[id^="tiptap-editor-"] .ProseMirror h5 {
  font-size: 0.83em !important;
}

[id^="tiptap-editor-"] .ProseMirror h6 {
  font-size: 0.75em !important;
}

/* Force paragraph spacing to match local */
[id^="tiptap-editor-"] .ProseMirror p {
  margin: 0.5em 0 !important;
  line-height: 1.6 !important;
}

/* Force list spacing to match local */
[id^="tiptap-editor-"] .ProseMirror ul,
[id^="tiptap-editor-"] .ProseMirror ol {
  margin: 0.5em 0 !important;
  padding-left: 1.5em !important;
  line-height: 1.6 !important;
}

[id^="tiptap-editor-"] .ProseMirror li {
  margin: 0.25em 0 !important;
  line-height: 1.6 !important;
}

/* File Loader Section */
.file-loader-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
}

.file-loader-section h2 {
  margin-top: 0;
  color: #333;
  font-size: 18px;
}

.file-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.file-controls select {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  min-width: 200px;
}

/* Comparison View */
.comparison-view {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

.original-content,
.converted-content {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background: white;
}

.original-content h3,
.converted-content h3 {
  margin-top: 0;
  color: #333;
  font-size: 16px;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 8px;
}

.content-preview {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  background: #fdfdfd;
  max-height: 300px;
  overflow-y: auto;
  font-size: 12px;
  line-height: 1.4;
}

.content-preview h1,
.content-preview h2,
.content-preview h3 {
  margin-top: 0;
}

.content-preview table {
  width: 100%;
  border-collapse: collapse;
  margin: 8px 0;
}

.content-preview table td {
  border: 1px solid #ddd;
  padding: 4px 8px;
}

textarea {
  width: 100%;
  height: 200px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  line-height: 1.3;
  resize: vertical;
  background: #f9f9f9;
}

/* Responsive layout */
@media (max-width: 1024px) {
  .comparison-view {
    grid-template-columns: 1fr;
  }
  
  .file-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .file-controls select {
    min-width: auto;
  }
}

/* Menu Bar - Works for both local (#app) and Bubble ([id^="tiptap-editor-"]) */
.menu-bar,
[id^="tiptap-editor-"] .menu-bar {
  display: flex;
  background: #f8f9fa;
  border: 1px solid #d0d7de;
  border-bottom: none;
  padding: 0;
  margin: 0;
  margin-bottom: 0;
  height: 20px;
  min-height: 20px;
  box-sizing: border-box;
  overflow: visible;
  border-radius: 6px 6px 0 0;
}

/* For Bubble - add !important to override Bubble's styles */
[id^="tiptap-editor-"] .menu-bar {
  display: flex !important;
  background: #f8f9fa !important;
  border: 1px solid #d0d7de !important;
  border-bottom: none !important;
  padding: 0 !important;
  margin: 0 !important;
  margin-bottom: 0 !important;
  height: 20px !important;
  min-height: 20px !important;
  box-sizing: border-box !important;
  overflow: visible !important;
  border-radius: 6px 6px 0 0 !important;
}

.menu-item,
[id^="tiptap-editor-"] .menu-item {
  padding: 0px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
  user-select: none;
  line-height: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

/* For Bubble - add !important to override Bubble's styles */
[id^="tiptap-editor-"] .menu-item {
  padding: 0px 12px !important;
  cursor: pointer !important;
  transition: background-color 0.2s !important;
  font-size: 12px !important;
  user-select: none !important;
  line-height: 20px !important;
  height: 20px !important;
  display: flex !important;
  align-items: center !important;
  box-sizing: border-box !important;
}

.menu-item:hover,
[id^="tiptap-editor-"] .menu-item:hover {
  background: #e9ecef;
}

/* For Bubble - add !important */
[id^="tiptap-editor-"] .menu-item:hover {
  background: #e9ecef !important;
}

.menu-item:last-child,
[id^="tiptap-editor-"] .menu-item:last-child {
  border-right: none;
}

/* For Bubble - add !important */
[id^="tiptap-editor-"] .menu-item:last-child {
  border-right: none !important;
}

/* Menu Dropdown */
.menu-dropdown {
  background: #ffffff;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  box-shadow: 0 8px 24px rgba(140, 149, 159, 0.2);
  min-width: 160px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
}

.dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  border-bottom: 1px solid #f6f8fa;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background: #f6f8fa;
}

.dropdown-item:last-child {
  border-bottom: none;
}

/* Enhanced Toolbar - TinyMCE Style - Works for both local and Bubble */
.editor-toolbar,
[id^="tiptap-editor-"] .editor-toolbar {
  border: 1px solid #cccccc;
  border-top: none;
  background: linear-gradient(to bottom, #f7f7f7 0%, #e8e8e8 100%);
  padding: 4px 6px;
  margin: 0;
  margin-bottom: 0;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.8);
  overflow: visible;
  box-sizing: border-box;
}

/* For Bubble - add !important to override Bubble's styles */
[id^="tiptap-editor-"] .editor-toolbar {
  border: 1px solid #cccccc !important;
  border-top: none !important;
  background: linear-gradient(to bottom, #f7f7f7 0%, #e8e8e8 100%) !important;
  padding: 4px 6px !important;
  margin: 0 !important;
  margin-bottom: 0 !important;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.8) !important;
  overflow: visible !important;
  box-sizing: border-box !important;
}

.toolbar-row {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-bottom: 2px;
  flex-wrap: wrap;
}

.toolbar-row:last-child {
  margin-bottom: 0;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 1px;
  margin-right: 6px;
  position: relative;
}

.toolbar-group::after {
  content: '';
  width: 1px;
  height: 20px;
  background: #d4d4d4;
  margin-left: 6px;
  margin-right: 0;
}

.toolbar-group:last-child::after {
  display: none;
}

/* Toolbar Buttons */
.toolbar-btn {
  padding: 2px 4px;
  font-size: 12px;
  border: 1px solid transparent;
  border-radius: 2px;
  background: transparent;
  cursor: pointer;
  transition: all 0.1s;
  min-width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-family: Arial, sans-serif;
}

.toolbar-btn:hover {
  background: linear-gradient(to bottom, #ffffff 0%, #f0f0f0 100%);
  border-color: #aaa #bbb #bbb #aaa;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.8);
}

.toolbar-btn:active,
.toolbar-btn.is-active {
  background: linear-gradient(to bottom, #e8e8e8 0%, #d0d0d0 100%);
  border-color: #888 #999 #999 #888;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

/* Format buttons (B, I, U, S) */
.format-btn {
  font-weight: normal;
  font-style: normal;
  text-decoration: none;
}

.format-btn strong,
.format-btn em,
.format-btn u,
.format-btn s {
  font-size: 12px;
  display: block;
}

/* Toolbar Selects */
.toolbar-select {
  padding: 2px 4px;
  border: 1px solid #ccc;
  border-radius: 2px;
  background: #ffffff;
  font-size: 11px;
  height: 22px;
  cursor: pointer;
  margin-right: 2px;
  min-width: 80px;
}

.toolbar-select:hover {
  border-color: #999;
}

.toolbar-select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 3px rgba(74, 144, 226, 0.3);
}

.font-size-select {
  min-width: 50px;
}

/* Color Controls */
.color-group {
  position: relative;
  display: flex;
  align-items: center;
}

.color-btn {
  position: relative;
  font-weight: bold;
  min-width: 24px;
}

.color-btn::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 14px;
  height: 3px;
  background: currentColor;
}

.highlight-btn::after {
  background: #ffff00;
}

.color-input {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 14px;
  height: 3px;
  border: none;
  padding: 0;
  opacity: 0;
  cursor: pointer;
}

.color-input:hover {
  opacity: 0.1;
}

#editor,
[id^="editor-"] {
  border: 1px solid #d0d7de;
  border-top: none;
  border-radius: 0 0 6px 6px;
  padding: 16px;
  min-height: 300px;
  max-height: 500px;
  min-width: 100%;
  width: 100%;
  overflow-y: auto;
  text-align: left;
  background: #fff;
  color: #333;
  box-sizing: border-box;
}

#editor,
[id^="editor-"] .ProseMirror {
  outline: none;
  min-height: 200px;
  min-width: 100%;
  width: 100%;
  max-height: none;
  cursor: text;
  box-sizing: border-box;
}

#editor .ProseMirror:empty::before,
[id^="editor-"] .ProseMirror:empty::before {
  content: "Start typing...";
  color: #999;
  pointer-events: none;
  font-style: italic;
}

#editor .ProseMirror p,
[id^="editor-"] .ProseMirror p {
  margin: 0.5em 0;
}

#editor .ProseMirror ul, 
#editor .ProseMirror ol,
[id^="editor-"] .ProseMirror ul, 
[id^="editor-"] .ProseMirror ol {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

#editor .ProseMirror li,
[id^="editor-"] .ProseMirror li {
  margin: 0.25em 0;
}

#editor .ProseMirror mark,
[id^="editor-"] .ProseMirror mark {
  background: #fef08a;
  border-radius: 2px;
  padding: 0.1em 0.2em;
}

#editor .ProseMirror ul[data-type="taskList"],
[id^="editor-"] .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding: 0;
}

#editor .ProseMirror ul[data-type="taskList"] li,
[id^="editor-"] .ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: center;
  margin: 0.5em 0;
}

#editor .ProseMirror ul[data-type="taskList"] li > label,
[id^="editor-"] .ProseMirror ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  user-select: none;
}

#editor .ProseMirror ul[data-type="taskList"] li > div,
[id^="editor-"] .ProseMirror ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

/* Table Styles */
#editor .ProseMirror table,
[id^="editor-"] .ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  overflow: hidden;
}

#editor .ProseMirror table td,
#editor .ProseMirror table th,
[id^="editor-"] .ProseMirror table td,
[id^="editor-"] .ProseMirror table th {
  min-width: 1em;
  border: 2px solid #ced4da;
  padding: 3px 5px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}

#editor .ProseMirror table th,
[id^="editor-"] .ProseMirror table th {
  font-weight: bold;
  text-align: left;
  background-color: #f8f9fa;
}

#editor .ProseMirror table .selectedCell:after,
[id^="editor-"] .ProseMirror table .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(200, 200, 255, 0.4);
  pointer-events: none;
}

#editor .ProseMirror table .column-resize-handle,
[id^="editor-"] .ProseMirror table .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #adf;
  pointer-events: none;
}

#editor .ProseMirror table p,
[id^="editor-"] .ProseMirror table p {
  margin: 0;
}

@media (prefers-color-scheme: dark) {
  #editor {
    background: #1a1a1a;
    color: #e0e0e0;
    border-color: #404040;
  }
  
  .editor-toolbar button {
    background: #2a2a2a;
    color: #e0e0e0;
    border-color: #404040;
  }
  
  .editor-toolbar button:hover {
    background: #3a3a3a;
  }
  
  .ProseMirror mark {
    background: #a16207;
  }
}

/* Table of Contents Styling */
.editor-content-wrapper {
  display: flex;
  position: relative;
}

.toc-sidebar {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background: #f8f9fa;
  border: 1px solid #d0d7de;
  border-left: 2px solid #0969da;
  border-radius: 6px;
  z-index: 100;
  overflow: hidden;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.toc-sidebar.hidden {
  display: none;
}

.toc-sidebar-header {
  padding: 12px 16px;
  background: #0969da;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toc-sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.toc-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.toc-close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.toc-sidebar-content {
  padding: 16px;
  height: calc(100% - 60px);
  overflow-y: auto;
}

.toc-empty {
  color: #656d76;
  font-style: italic;
  text-align: center;
  margin: 0;
}

.toc-item {
  padding: 8px 12px;
  margin: 4px 0;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;
  display: block;
  text-decoration: none;
  color: #24292f;
}

.toc-item:hover {
  background: #f3f4f6;
  text-decoration: none;
  color: #24292f;
}

.toc-item.active {
  background: #dbeafe;
  border-left-color: #0969da;
  color: #0969da;
  font-weight: 500;
}

.toc-item-level-1 { padding-left: 12px; font-weight: 600; }
.toc-item-level-2 { padding-left: 24px; }
.toc-item-level-3 { padding-left: 36px; }
.toc-item-level-4 { padding-left: 48px; }
.toc-item-level-5 { padding-left: 60px; }
.toc-item-level-6 { padding-left: 72px; }

/* TOC Dropdown */
.toc-dropdown {
  position: absolute;
  min-width: 250px;
  max-width: 400px;
  max-height: 300px;
  overflow-y: auto;
  background: white;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 8px 0;
}

.toc-dropdown .toc-empty {
  padding: 16px;
  text-align: center;
}

/* Inserted TOC in document */
.document-toc {
  background: #f8f9fa;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
}

.document-toc h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #24292f;
  border-bottom: 1px solid #d0d7de;
  padding-bottom: 8px;
}

.document-toc ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.document-toc li {
  margin: 4px 0;
}

.document-toc a {
  color: #0969da;
  text-decoration: none;
  padding: 4px 0;
  display: block;
}

.document-toc a:hover {
  text-decoration: underline;
}

/* Fix spacing issues in document TOC */
.document-toc ul {
  margin: 8px 0;
  padding-left: 20px;
}

.document-toc li {
  margin: 2px 0;
  padding: 0;
  line-height: 1.4;
}

.document-toc li p {
  margin: 0;
  padding: 0;
  display: inline;
}

.document-toc a {
  margin: 0;
  padding: 2px 0;
}


/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .toc-sidebar {
    background: #1a1a1a;
    border-color: #404040;
  }
  
  .toc-sidebar-header {
    background: #0969da;
  }
  
  .toc-item {
    color: #e0e0e0;
  }
  
  .toc-item:hover {
    background: #2a2a2a;
    color: #e0e0e0;
  }
  
  .toc-dropdown {
    background: #1a1a1a;
    border-color: #404040;
  }
  
  .document-toc {
    background: #2a2a2a;
    border-color: #404040;
  }
  
  .document-toc h3 {
    color: #e0e0e0;
    border-color: #404040;
  }
}

/* Bubble Menu Styling */
.bubble-menu {
  display: flex;
  background: white;
  border: 1px solid #d0d7de;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px;
  gap: 2px;
  opacity: 0;
  transition: opacity 0.2s;
  z-index: 100;
}

.bubble-menu.show {
  opacity: 1;
}

.bubble-btn {
  background: none;
  border: none;
  padding: 8px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #24292f;
  transition: background-color 0.2s;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bubble-btn:hover {
  background: #f3f4f6;
}

.bubble-btn:active {
  background: #e5e7eb;
}

.bubble-btn.is-active {
  background: #dbeafe;
  color: #0969da;
}

/* Floating Menu Styling */
.floating-menu {
  display: flex;
  background: white;
  border: 1px solid #d0d7de;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px;
  gap: 2px;
  opacity: 0;
  transition: opacity 0.2s;
  z-index: 100;
}

.floating-menu.show {
  opacity: 1;
}

.floating-btn {
  background: none;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #24292f;
  transition: background-color 0.2s;
  min-width: 36px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-btn:hover {
  background: #f3f4f6;
}

.floating-btn:active {
  background: #e5e7eb;
}

/* Dark mode adjustments for floating menus */
@media (prefers-color-scheme: dark) {
  .bubble-menu, .floating-menu {
    background: #1a1a1a;
    border-color: #404040;
  }
  
  .bubble-btn, .floating-btn {
    color: #e0e0e0;
  }
  
  .bubble-btn:hover, .floating-btn:hover {
    background: #2a2a2a;
  }
  
  .bubble-btn:active, .floating-btn:active {
    background: #3a3a3a;
  }
  
  .bubble-btn.is-active {
    background: #0969da;
    color: white;
  }
}

/* Document Settings Modal */
.document-settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.document-settings-modal.hidden {
  display: none;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.modal-content {
  position: relative;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.modal-close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.settings-section {
  margin-bottom: 32px;
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.setting-group {
  margin-bottom: 16px;
}

.setting-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.setting-label input[type="checkbox"] {
  margin-right: 8px;
}

.setting-input,
.setting-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s;
}

.setting-input:focus,
.setting-select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.setting-input::placeholder {
  color: #999;
}

/* Preview Container */
.preview-container {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
}

.preview-page {
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  min-height: 200px;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 12px;
}

.preview-header {
  padding: 8px 16px;
  border-bottom: 1px solid #e0e0e0;
  background: #f9f9f9;
  text-align: center;
  font-weight: 600;
  color: #333;
  min-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-content {
  padding: 16px;
  min-height: 120px;
  color: #666;
  line-height: 1.4;
}

.preview-content p {
  margin: 0 0 8px 0;
}

.preview-footer {
  padding: 8px 16px;
  border-top: 1px solid #e0e0e0;
  background: #f9f9f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #555;
  min-height: 24px;
}

.footer-left,
.footer-center,
.footer-right {
  flex: 1;
  text-align: center;
}

.footer-left {
  text-align: left;
}

.footer-right {
  text-align: right;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #f8f9fa;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: #4a90e2;
  color: white;
}

.btn-primary:hover {
  background: #357abd;
}

.btn-secondary {
  background: #e0e0e0;
  color: #555;
}

.btn-secondary:hover {
  background: #d0d0d0;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .modal-content {
    background: #1a1a1a;
    color: #e0e0e0;
  }
  
  .modal-header {
    background: #2a2a2a;
    border-color: #404040;
  }
  
  .modal-header h3 {
    color: #e0e0e0;
  }
  
  .modal-close-btn {
    color: #ccc;
  }
  
  .modal-footer {
    background: #2a2a2a;
    border-color: #404040;
  }
  
  .settings-section h4 {
    color: #e0e0e0;
    border-color: #404040;
  }
  
  .setting-label {
    color: #ccc;
  }
  
  .setting-input,
  .setting-select {
    background: #333;
    border-color: #555;
    color: #e0e0e0;
  }
  
  .setting-input::placeholder {
    color: #888;
  }
  
  .preview-container {
    background: #2a2a2a;
    border-color: #404040;
  }
  
  .preview-page {
    background: #333;
    border-color: #555;
  }
  
  .preview-header,
  .preview-footer {
    background: #404040;
    border-color: #555;
    color: #e0e0e0;
  }
  
  .preview-content {
    color: #ccc;
  }
}
